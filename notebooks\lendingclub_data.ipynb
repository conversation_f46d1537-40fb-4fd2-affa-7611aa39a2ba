import pandas as pd
import pathlib as pl

DATA_PATH = pl.Path.cwd() / "data" / "Lending Club loan data" / "loan.csv"


# df = pd.read_csv(DATA_PATH, low_memory=False, sep=',')

chunk_size = 10000  # Adjust based on your memory
chunks = []

for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):
    # Process each chunk (filter, transform, etc.)
    processed_chunk = chunk  # Add your processing here
    chunks.append(processed_chunk)

# Combine if needed (or process chunks separately)
df = pd.concat(chunks, ignore_index=True)

df.shape

