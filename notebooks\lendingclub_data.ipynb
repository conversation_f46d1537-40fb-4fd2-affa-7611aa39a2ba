import pandas as pd
import pathlib as pl

DATA_PATH = "D:\Dhia\ING INFO 2\Stage d'ete\Proxym\Loan Request Approval Prediction\\data\Lending Club loan data\loan.csv"

# df = pd.read_csv(DATA_PATH, low_memory=True,  sep=',')

chunk_size = 5000

# Initialize empty list (optional if aggregating later)
chunks = []

# Read CSV in chunks
for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):
    # Process or append each chunk
    chunks.append(chunk)

# Concatenate all chunks (if needed)
df = pd.concat(chunks, ignore_index=True)

